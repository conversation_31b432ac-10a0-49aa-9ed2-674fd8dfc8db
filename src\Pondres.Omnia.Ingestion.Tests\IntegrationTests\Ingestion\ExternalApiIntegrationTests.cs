﻿using FluentAssertions;
using Pondres.Omnia.Ingestion.Tests.Fixtures;
using Pondres.Omnia.Ingestion.V2.Contracts.Events;
using Pondres.Omnia.OrderHub.Contracts.Order;
using Xunit;

namespace Pondres.Omnia.Ingestion.Tests.IntegrationTests.Ingestion;

[Collection("IntegrationTests")]
public class ExternalApiIntegrationTests(IntegrationTestFixture fixture) : BaseIntegrationTest(fixture)
{
    private async Task AssertSuccessfulIngestion(string customer, string customerReference, CreateOrderMessage orderCreated)
    {
        fixture.ExternalApiApp.BusPublisherObserver.Should().NotHaveSeenMessage<IngestionFailure>(x => x.Customer == customer);

        orderCreated.Source.Should().Be("ExternalApiIngestion");
        orderCreated.Customer.Should().Be(customer);
        orderCreated.OrderId.Should().NotBe(Guid.Empty);
        orderCreated.CustomerReference.Should().Be(customerReference);
        orderCreated.Categories.Should().NotBeNull();
        orderCreated.DataContainerName.Should().Be("orderdata");

        var file = await fixture.Blob.DownloadOrderDataAsync<SampleOrderData>(
            orderCreated,
            $"{orderCreated.Flow}/{orderCreated.OrderId}/RawData/{orderCreated.RequestId}.json");

        file.CustomerReference.Should().Be(customerReference);
    }
}
