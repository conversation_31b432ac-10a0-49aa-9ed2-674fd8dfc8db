﻿using FluentAssertions;
using Newtonsoft.Json;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Tests.Clients.Internal;
using Pondres.Omnia.Ingestion.Tests.Fixtures;
using Pondres.Omnia.Ingestion.V2.Contracts.Events;
using Pondres.Omnia.OrderHub.Contracts.Order;
using System.Text;
using Xunit;

namespace Pondres.Omnia.Ingestion.Tests.IntegrationTests.Ingestion;

[Collection("IntegrationTests")]
public class InternalApiIntegrationTests(IntegrationTestFixture fixture) : BaseIntegrationTest(fixture)
{
    [Fact]
    public async Task IngestInternalOrderAsync_ValidOrder_OrderCreated()
    {
        // Arrange
        var customer = GetCustomer();
        var client = GetInternalClient();

        fixture.InternalApiApp.CustomerRequestSender.SetupSuccessCustomerResponse(customer);

        var customerReference = Guid.NewGuid().ToString();
        var flow = Guid.NewGuid().ToString();
        var categories = new OrderCreationModelCategories
        {
            One = Guid.NewGuid().ToString(),
            Two = Guid.NewGuid().ToString(),
            Three = Guid.NewGuid().ToString()
        };

        var stream = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new SampleOrderData(customerReference))));

        // Act
        await client.SubmitAsync(
            x_customer_reference: customerReference,
            x_flow: flow,
            x_flow_id: null,
            x_customer: customer,
            x_category_one: categories.One,
            x_category_two: categories.Two,
            x_category_three: categories.Three,
            x_token: "dev",
            content: new FileParameter(stream));

        // Assert
        var orderCreated = fixture.InternalApiApp.BusPublisherObserver.Should().HaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);

        await AssertSuccessfulIngestion(customer, customerReference, orderCreated);

        orderCreated.Flow.Should().Be(flow);
    }

    [Fact]
    public async Task IngestInternalOrderAsync_ValidOrder_FlowId_OrderCreated()
    {
        // Arrange
        var customer = GetCustomer();
        var client = GetInternalClient();

        fixture.InternalApiApp.CustomerRequestSender.SetupSuccessCustomerResponse(customer);

        var customerReference = Guid.NewGuid().ToString();
        var flow = Guid.NewGuid().ToString();
        var categories = new OrderCreationModelCategories
        {
            One = Guid.NewGuid().ToString(),
            Two = Guid.NewGuid().ToString(),
            Three = Guid.NewGuid().ToString()
        };

        var stream = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new SampleOrderData(customerReference))));

        // Act
        await client.SubmitAsync(
            x_customer_reference: customerReference,
            x_flow: null,
            x_flow_id: flow,
            x_customer: customer,
            x_category_one: categories.One,
            x_category_two: categories.Two,
            x_category_three: categories.Three,
            x_token: "dev",
            content: new FileParameter(stream));

        // Assert
        var orderCreated = fixture.InternalApiApp.BusPublisherObserver.Should().HaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);

        await AssertSuccessfulIngestion(customer, customerReference, orderCreated);

        orderCreated.FlowId.Should().Be(flow);
    }

    [Fact]
    public async Task IngestInternalOrderAsync_ValidationError_IngestionFailed()
    {
        // Arrange
        var customer = GetCustomer();
        var client = GetInternalClient();

        fixture.InternalApiApp.CustomerRequestSender.SetupSuccessCustomerResponse(customer);

        var stream = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new SampleOrderData(string.Empty))));

        // Act
        await Assert.ThrowsAsync<ApiException<ApiResultDto>>(() => client.SubmitAsync(
            x_customer_reference: string.Empty,
            x_flow: string.Empty,
            x_flow_id: null,
            x_customer: customer,
            x_category_one: string.Empty,
            x_category_two: string.Empty,
            x_category_three: string.Empty,
            x_token: "dev",
            content: new FileParameter(stream)));

        // Assert
        fixture.InternalApiApp.BusPublisherObserver.Should().HaveSeenMessage<IngestionFailure>(x => x.Customer == customer);
    }

    [Fact]
    public async Task IngestInternalOrderAsync_ValidOrder_UnexpectedException_IngestionFailed()
    {
        // Arrange
        var customer = GetCustomer();
        var client = GetInternalClient();

        var customerReference = Guid.NewGuid().ToString();
        var flow = Guid.NewGuid().ToString();
        var categories = new OrderCreationModelCategories
        {
            One = Guid.NewGuid().ToString(),
            Two = Guid.NewGuid().ToString(),
            Three = Guid.NewGuid().ToString()
        };

        var stream = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new SampleOrderData(customerReference))));

        // Act
        await Assert.ThrowsAsync<ApiException>(() => client.SubmitAsync(
            x_customer_reference: customerReference,
            x_flow: flow,
            x_customer: customer,
            x_flow_id: null,
            x_category_one: categories.One,
            x_category_two: categories.Two,
            x_category_three: categories.Three,
            x_token: "dev",
            content: new FileParameter(stream)));

        // Assert
        fixture.InternalApiApp.BusPublisherObserver.Should().HaveSeenMessage<IngestionFailure>(x => x.Customer == customer);
    }

    private async Task AssertSuccessfulIngestion(string customer, string customerReference, CreateOrderMessage orderCreated)
    {
        fixture.InternalApiApp.BusPublisherObserver.Should().NotHaveSeenMessage<IngestionFailure>(x => x.Customer == customer);

        orderCreated.Source.Should().Be("InternalApiIngestion");
        orderCreated.Customer.Should().Be(customer);
        orderCreated.OrderId.Should().NotBe(Guid.Empty);
        orderCreated.CustomerReference.Should().Be(customerReference);
        orderCreated.Categories.Should().NotBeNull();
        orderCreated.DataContainerName.Should().Be("orderdata");

        var file = await fixture.Blob.DownloadOrderDataAsync<SampleOrderData>(
            orderCreated,
            $"{orderCreated.Flow}/{orderCreated.OrderId}/RawData/{orderCreated.RequestId}.json");

        file.CustomerReference.Should().Be(customerReference);
    }
}
