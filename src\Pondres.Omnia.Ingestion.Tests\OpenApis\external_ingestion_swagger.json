{"openapi": "3.0.4", "info": {"title": "Pondres.Omnia.Ingestion.Api.External", "version": "1.0"}, "servers": [{"url": "/ingestion/api"}], "paths": {"/submit": {"post": {"tags": ["ExternalIngestion"], "parameters": [{"name": "x-customer-reference", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "x-flow", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "x-flow-id", "in": "header", "schema": {"type": "string"}}, {"name": "x-customer", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "x-category-one", "in": "header", "schema": {"type": "string"}}, {"name": "x-category-two", "in": "header", "schema": {"type": "string"}}, {"name": "x-category-three", "in": "header", "schema": {"type": "string"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"content": {"type": "string", "format": "binary"}}}, "encoding": {"content": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderCreatedResponseApiResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderCreatedResponseApiResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderCreatedResponseApiResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}}}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResultDto"}}}}}}}, "/trigger": {"post": {"tags": ["ExternalTrigger"], "parameters": [{"name": "x-customer", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "x-customer-reference", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/TriggerRequestModel"}]}}, "text/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/TriggerRequestModel"}]}}, "application/*+json": {"schema": {"allOf": [{"$ref": "#/components/schemas/TriggerRequestModel"}]}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}}, "components": {"schemas": {"ApiResultDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderCreatedResponse": {"type": "object", "properties": {"orderId": {"type": "string", "format": "uuid"}, "flow": {"type": "string"}, "customerReference": {"type": "string"}}, "additionalProperties": false}, "OrderCreatedResponseApiResultDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "value": {"allOf": [{"$ref": "#/components/schemas/OrderCreatedResponse"}], "nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "TriggerRequestModel": {"type": "object", "properties": {"subType": {"type": "string", "nullable": true}, "jsonFileData": {"nullable": true}}, "additionalProperties": false}}}}