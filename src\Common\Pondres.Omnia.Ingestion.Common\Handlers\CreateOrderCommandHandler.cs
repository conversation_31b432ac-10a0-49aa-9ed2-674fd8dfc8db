﻿using FluentValidation;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Common.Service;
using Serilog;
using Solude.Packages.Common;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Handlers;

public class CreateOrderCommandValidator : AbstractValidator<CreateOrderCommand>
{
    public CreateOrderCommandValidator()
    {
        RuleFor(x => x.Customer).NotEmpty();
        RuleFor(x => x.CustomerReference).NotEmpty();
        RuleFor(x => x.FlowName).NotEmpty().When(x => string.IsNullOrEmpty(x.FlowId));
        RuleFor(x => x.FlowId).NotEmpty().When(x => string.IsNullOrEmpty(x.FlowName));
    }
}

public record CreateOrderCommand(
    string Customer,
    string CustomerReference,
    string FlowName,
    string? FlowId,
    string Source,
    OrderCreationModelCategories Categories);

public record OrderCreatedResponse(Guid OrderId, string Flow, string CustomerReference);

public class CreateOrderCommandHandler(
    CustomerProvider customerProvider,
    IEventService eventService,
    IOrderService orderService)
{
    public async Task<CommandResult<OrderCreatedResponse>> HandleAsync(CreateOrderCommand command, Stream content)
    {
        var requestId = Guid.NewGuid().ToString();

        var validationResult = await new CreateOrderCommandValidator().ValidateAsync(command);

        if (!validationResult.IsValid)
        {
            await eventService.PublishIngestionFailureAsync(
                message: "Validation failed",
                data: validationResult.ToString(),
                requestId: requestId,
                flowName: command.FlowName,
                customer: command.Customer);

            return CommandResult<OrderCreatedResponse>.ValidationError(validationResult);
        }

        try
        {
            var customer = await customerProvider.GetCustomerDetailsAsync(command.Customer);

            var creationModel = new OrderCreationModel(
                OrderId: Guid.NewGuid(),
                FileName: $"{requestId}.json",
                RequestId: requestId,
                Customer: command.Customer,
                CustomerReference: command.CustomerReference,
                FlowName: command.FlowName,
                FlowId: command.FlowId,
                SourceName: command.Source,
                Categories: command.Categories,
                CustomerStorageAccountName: customer.Customer.StorageAccountName);

            content.Seek(0, SeekOrigin.Begin);

            await orderService.CreateSingleRawDataFileOrderAsync(content, creationModel);

            return CommandResult<OrderCreatedResponse>.Successful(new OrderCreatedResponse(OrderId: creationModel.OrderId, Flow: creationModel.FlowName, CustomerReference: creationModel.CustomerReference));
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to ingest internal order.");

            await eventService.PublishIngestionFailureAsync(
                exception: ex,
                requestId: requestId,
                flowName: command.FlowName,
                customer: command.Customer);

            return CommandResult<OrderCreatedResponse>.Error(message: ex.Message);
        }
    }
}
